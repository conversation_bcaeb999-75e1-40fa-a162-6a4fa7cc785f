import { useCallback, useState } from 'react'
import { Box, Button, IconButton, Tooltip, type ButtonProps } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import CopyIcon from '@mui/icons-material/CopyAll'
import FileCopyIcon from '@mui/icons-material/FileCopy'
import {
  closeSnackbar,
  enqueueSnackbar,
  type OptionsObject,
  type SnackbarMessage,
} from 'notistack'
import CopyToClipboard from 'react-copy-to-clipboard'
import type { Except, RequireAtLeastOne } from 'type-fest'

import { ctIntl } from 'src/util-components/ctIntl'

export const enqueueSnackbarWithCloseAction = (
  message: SnackbarMessage,
  options: RequireAtLeastOne<OptionsObject, 'variant'>,
  copyOptions?: {
    passwordCopiedToClipboard: boolean
    setPasswordCopiedToClipboard: (value: boolean) => void
  },
) => {
  const passwordCopiedToClipboard = copyOptions?.passwordCopiedToClipboard ?? false
  const setPasswordCopiedToClipboard =
    copyOptions?.setPasswordCopiedToClipboard ?? (() => {})

  console.log(
    'passwordCopiedToClipboard',
    passwordCopiedToClipboard,
    copyOptions?.passwordCopiedToClipboard,
  )

  return enqueueSnackbar(
    <Box
      display="flex"
      alignItems="center"
      gap={1}
    >
      {message}
      <CopyToClipboard
        text={message as string}
        onCopy={() => setPasswordCopiedToClipboard(true)}
      >
        <Tooltip
          title={ctIntl.formatMessage({
            id: passwordCopiedToClipboard ? 'Copied Successfully' : 'Copy to Clipboard',
          })}
          placement="top"
        >
          <IconButton
            size="small"
            data-testid={`CopyButton-${message}`}
          >
            {passwordCopiedToClipboard ? (
              <FileCopyIcon
                fontSize="small"
                sx={{ color: 'white' }}
              />
            ) : (
              <CopyIcon
                fontSize="small"
                sx={{ color: 'white' }}
              />
            )}
          </IconButton>
        </Tooltip>
      </CopyToClipboard>
    </Box>,
    {
      action: (snackbarId) => (
        <>
          <IconButton
            size="small"
            onClick={() => closeSnackbar(snackbarId)}
          >
            <CloseIcon
              fontSize="small"
              sx={{ color: 'white' }}
            />
          </IconButton>
        </>
      ),
      ...options,
    },
  )
}

export type EnqueueSnackbarWithCloseAction = typeof enqueueSnackbarWithCloseAction

export const enqueueSnackbarWithButtonAction = ({
  message,
  snackBarOptions,
  buttonAction,
  buttonText,
}: {
  message: SnackbarMessage
  snackBarOptions: RequireAtLeastOne<OptionsObject, 'variant'>
  buttonText: string
  buttonAction: () => void
  buttonProps?: Except<ButtonProps, 'onClick'>
}) =>
  enqueueSnackbar(message, {
    action: (snackbarId) => (
      <>
        <Button
          variant="text"
          color="inherit"
          onClick={() => {
            buttonAction()
            closeSnackbar(snackbarId)
          }}
        >
          {buttonText}
        </Button>
        <IconButton
          size="small"
          onClick={() => closeSnackbar(snackbarId)}
        >
          <CloseIcon
            fontSize="small"
            sx={{ color: 'white' }}
          />
        </IconButton>
      </>
    ),
    ...snackBarOptions,
  })

export const useSnackbarWithCloseAction = () => {
  const [passwordCopiedToClipboard, setPasswordCopiedToClipboard] = useState(false)

  return useCallback(
    (message: SnackbarMessage, options: RequireAtLeastOne<OptionsObject, 'variant'>) =>
      enqueueSnackbarWithCloseAction(message, options, {
        passwordCopiedToClipboard,
        setPasswordCopiedToClipboard,
      }),
    [passwordCopiedToClipboard],
  )
}

export type UseSnackbarWithCloseActionReturnType = ReturnType<
  typeof useSnackbarWithCloseAction
>
