import { GridLogicOperator } from '@karoo-ui/core'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import * as R from 'remeda'
import { z } from 'zod'

import { makeQueryErrorHandlerWithToast } from 'src/api/helpers'
import {
  createFilterItemSchema_Date,
  createFilterItemSchema_String,
} from 'src/shared/data-grid/server-client/server-filter-model-schemas'
import type {
  ServerFilterItem_Date,
  ServerFilterItem_String,
} from 'src/shared/data-grid/server-client/types'
import type { PromiseResolvedType } from 'src/types/global'
import { createQuery } from 'src/util-functions/react-query-utils'

import { secondsToMs } from 'cartrack-utils'
import { getServerPaginationIssuanceList } from './api'

export type Metric =
  | 'ACTIVE'
  | 'ACTIVE_ALMOST_LATE'
  | 'ACTIVE_LATE'
  | 'APPROVED'
  | 'CANCELLED'
  | 'DECLINED'
  | 'EXPIRING_APPROVAL'
  | 'FREE'
  | 'REQUESTED'
  | 'RETURNED'
  | 'RETURNED_LATE'
  | 'TOTAL'
  | 'FORCE_TERMINATED'

export const sortableColumnIds = [
  'requestDate',
  'pickUpAt',
  'startDate',
  'endDate',
  'returnedAt',
] as const

export type SortableColumnId = (typeof sortableColumnIds)[number]

export declare namespace FetchServerPaginationIssuanceList {
  type ApiInput = {
    pagination: {
      pageSize: number
      offset: number
    }
    sort: {
      field: SortableColumnId
      sort: 'asc' | 'desc'
    }
    filter: {
      items: Array<FilterItem>
      logicOperator: GridLogicOperator
      searchTextFilterValues?: Array<string>
      /**
       * - `GridLogicOperator.And`: the row must pass all the values.
       * - `GridLogicOperator.Or`: the row must pass at least one value.
       */
      searchTextFilterLogicOperator?: GridLogicOperator
    }
    statusIds: string | null
  }
  type Return = PromiseResolvedType<typeof getServerPaginationIssuanceList>

  type ApiOutput = {
    value: Array<Booking>
    metrics: Metrics
    error: string | null
    isServerError: boolean
  }

  type Booking = {
    id: number
    statusId: number
    status: string

    startDate: string
    endDate: string

    description: string
    purpose: string

    vehicleId: number
    vehicleRegistration?: string

    createdDate: string
    updatedDate?: string

    pickupTime?: string | null
    dropoffTime?: string | null

    type: string
    requestClientUserId: string
    requestedBy: string
    requestedDate: string

    journeys: Array<Journey>
    locationType: number | null
    pickupLocationId: number
    pickupLocationName: string

    requestClientDriverId: string | null
    driverName?: string
    driverEmail?: string

    bookingPurposeId: number
    bookingPurposeTitle: string
    bookingPurposeDescription: string

    vehicleCategoryId: number | null
    vehicleCategoryName: string | null

    bookingReference?: string
    keyReturnTs?: string | null
    keyCollectionTs?: string | null
    accessories: Array<Accessory>
    approvedManagers: Array<ApprovedRejectedManager>
    rejectedManagers: Array<ApprovedRejectedManager>

    commanderClientUserId: string
    commanderUsername: string
  }

  type Journey = {
    id: number
    location: string
    startTime?: string
    endTime?: string
    order: number
  }

  type Accessory = {
    id: number
    typeId: number
    name: string
    description: string
  }

  type ApprovedRejectedManager = {
    clientUserId: string
    username: string
  }

  type Metrics = {
    total: number
    statusMetrics: Array<StatusMetric>
  }

  type StatusMetric = {
    statusId: number
    count: number
  }
}

export type Booking = FetchServerPaginationIssuanceList.Return['bookings'][number]

type FilterItem =
  | ServerFilterItem_String<'vehicle'>
  | ServerFilterItem_String<'driver'>
  | ServerFilterItem_String<'driverEmail'>
  | ServerFilterItem_String<'vehicleType'>
  | ServerFilterItem_String<'purpose'>
  | ServerFilterItem_String<'vehicleCommander'>
  | ServerFilterItem_String<'requestor'>
  | ServerFilterItem_Date<'requestDate'>
  | ServerFilterItem_Date<'startDate'>

export const fetchIssuanceListFilterModelSchema = {
  items: {
    vehicle: createFilterItemSchema_String({ field: 'vehicle' }),
    driver: createFilterItemSchema_String({ field: 'driver' }),
    driverEmail: createFilterItemSchema_String({ field: 'driverEmail' }),
    vehicleType: createFilterItemSchema_String({ field: 'vehicleType' }),
    purpose: createFilterItemSchema_String({ field: 'purpose' }),
    requestor: createFilterItemSchema_String({ field: 'requestor' }),
    vehicleCommander: createFilterItemSchema_String({ field: 'vehicleCommander' }),
    requestDate: createFilterItemSchema_Date({
      field: 'requestDate',
      operators: ['range'],
    })(({ create, field, operators }) =>
      create({ field, operators: z.enum(operators) }),
    ),
    startDate: createFilterItemSchema_Date({
      field: 'startDate',
      operators: ['range'],
    })(({ create, field, operators }) =>
      create({ field, operators: z.enum(operators) }),
    ),
  },
  get self() {
    const items = this.items
    return z.object({
      items: z.array(
        z.union([
          items.vehicle,
          items.driver,
          items.driverEmail,
          items.vehicleType,
          items.purpose,
          items.vehicleCommander,
          items.requestor,
          items.requestDate,
          items.startDate,
          // TODO: add more fields
        ]),
      ),
      logicOperator: z.nativeEnum(GridLogicOperator),
      quickFilterValues: z.array(z.string()),
    })
  },
}

export const filterableColumnIds = R.values(
  fetchIssuanceListFilterModelSchema.items,
).map((item) => item.shape.field.value)

export type FilterableColumnId = (typeof filterableColumnIds)[number]

export type FetchIssuanceListFilterModelSchemaSelf = z.infer<
  typeof fetchIssuanceListFilterModelSchema.self
>

const generateKey = (params: FetchServerPaginationIssuanceList.ApiInput) =>
  [...baseServerPaginationIssuanceListQueryKey, params] as const

export const baseServerPaginationIssuanceListQueryKey = [
  'carpool/serverPagination-issuanceList',
]

const issuanceListQuery = (params: FetchServerPaginationIssuanceList.ApiInput) =>
  createQuery({
    queryKey: generateKey(params),
    queryFn: () => getServerPaginationIssuanceList(params),
    staleTime: secondsToMs(20),
    // NOTE: this placehoderData is used to keep the previous data while fetching new data
    placeholderData: keepPreviousData,
    ...makeQueryErrorHandlerWithToast(),
  })

const useServerPaginationIssuanceListQuery = (
  params: FetchServerPaginationIssuanceList.ApiInput,
) => useQuery(issuanceListQuery(params))

export default useServerPaginationIssuanceListQuery
