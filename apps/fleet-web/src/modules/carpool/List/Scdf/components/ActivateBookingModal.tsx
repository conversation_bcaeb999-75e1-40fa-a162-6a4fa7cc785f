import { useContext } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Autocomplete,
  Box,
  CircularProgress,
  DateTimePicker,
  Stack,
  TextField,
} from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'

import {
  driverIdSchema,
  vehicleIdSchema,
  type CarpoolBookingId,
  type DriverId,
  type VehicleId,
} from 'api/types'
import { getAuthenticatedUser, getFacilitiesTranslatorFn } from 'duxs/user'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { useSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import useAvailableVehicleOptions from 'src/modules/carpool/components/ScdfIssuanceRequestDrawer/hooks/useAvailableVehicleOptions'
import { DriverSingleSelect } from 'src/modules/shared/DriverSingleSelect'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/formik'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import UserAutocomplete from '../../../components/UserAutocomplete'
import { CarpoolOptionsContext } from '../../constants'
import useActivateBookingMutation from '../api/useActivateBookingMutation'

// Form schema
const schema = z.object({
  pickUpTime: z.string().min(1, { message: messages.required }),
  actualPickUpTime: z.string().min(1, { message: messages.required }),
  vehicleId: vehicleIdSchema,
  driverId: driverIdSchema,
  vehicleCommander: z.string().min(1, { message: messages.required }),
})

type FormData = z.infer<typeof schema>

type Props = {
  onClose: () => void
  bookingId: CarpoolBookingId
  initialValues: {
    pickUpTime: string
    vehicleId: VehicleId
    driverId: DriverId
    vehicleCommander: string
    vehicleTypeId: number | null
    locationId: number | null
    purposeid: number | null
  }
}

function ActivateBookingModal({ onClose, bookingId, initialValues }: Props) {
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const { carpoolOptionsData } = useContext(CarpoolOptionsContext)
  const mutation = useActivateBookingMutation()
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)
  const enqueueSnackbarWithCloseAction = useSnackbarWithCloseAction()

  const {
    control,
    handleSubmit,
    setValue: setFormValue,
    formState: { isValid },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    mode: 'all',
    defaultValues: {
      pickUpTime: initialValues?.pickUpTime || '',
      actualPickUpTime: '',
      vehicleId: initialValues?.vehicleId,
      driverId: initialValues?.driverId,
      vehicleCommander: initialValues?.vehicleCommander || '',
    },
  })

  const handleFormSubmit = handleSubmit((data) => {
    enqueueSnackbarWithCloseAction(
      ctIntl.formatMessage({ id: 'Approve booking successfully.' }),
      { variant: 'success' },
    )
    // mutation.mutate(
    //   {
    //     bookingId,
    //     pickUpTime: new Date(data.pickUpTime),
    //     actualPickUpTime: new Date(data.actualPickUpTime),
    //     vehicleId: data.vehicleId,
    //     driverId: data.driverId,
    //     vehicleCommander: data.vehicleCommander,
    //     approver: clientUserId ?? '',
    //   },
    //   {
    //     onSuccess() {
    //       onClose()
    //     },
    //   },
    // )
  })

  const selectedVehicleId = useWatch({ control, name: 'vehicleId' })
  const selectedDriverId = useWatch({ control, name: 'driverId' })
  const facilityLabel = translateFacilitiesTerm('Facility')

  const { availableVehicleOptions, isPending } = useAvailableVehicleOptions({
    isLoadingCarpoolOptionsData: false,
    selectedVehicleId,
    selectedVehicleTypeId: initialValues.vehicleTypeId,
    carpoolOptionsData,
    selectedDriverId,
    selectedPurposeOfRequestId: initialValues.purposeid,
    selectedLocationId: initialValues.locationId,
    facilityLabel,
    showSelectedVehicle: false,
  })

  return (
    <ConfirmationModal
      title="Activate Booking"
      open
      onClose={onClose}
      onConfirm={handleFormSubmit}
      isLoading={mutation.isPending}
      disabledConfirmButton={!isValid}
      confirmButtonLabel="CONFIRM"
      cancelButtonLabel="CANCEL"
    >
      <Stack spacing={2}>
        <IntlTypography
          color="text.secondary"
          msgProps={{
            id: 'tfms.activateBooking.description',
            values: { bookingId },
          }}
        />

        <Controller
          control={control}
          name="pickUpTime"
          render={({ field, fieldState }) => (
            <DateTimePicker
              label={ctIntl.formatMessage({ id: 'Pick Up Time' })}
              value={field.value ? DateTime.fromISO(field.value) : null}
              onChange={(newValue) =>
                setFormValue(
                  'pickUpTime',
                  newValue && newValue.isValid ? newValue.toISO() : '',
                  { shouldValidate: true },
                )
              }
              slotProps={{
                textField: {
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  error: !!fieldState.error,
                  size: 'small',
                },
              }}
            />
          )}
        />

        <Controller
          control={control}
          name="actualPickUpTime"
          render={({ field, fieldState }) => (
            <DateTimePicker
              label={ctIntl.formatMessage({ id: 'Actual Pick Up Time' })}
              value={field.value ? DateTime.fromISO(field.value) : null}
              onChange={(newValue) =>
                setFormValue(
                  'actualPickUpTime',
                  newValue && newValue.isValid ? newValue.toISO() : '',
                  { shouldValidate: true },
                )
              }
              slotProps={{
                textField: {
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  error: !!fieldState.error,
                  size: 'small',
                },
              }}
            />
          )}
        />

        <Controller
          control={control}
          name="vehicleId"
          render={({ field, fieldState }) => (
            <Autocomplete
              {...getAutocompleteVirtualizedProps({
                options: availableVehicleOptions.array,
              })}
              getOptionLabel={(option) => option.label}
              value={availableVehicleOptions.byId.get(field.value) || null}
              onChange={(_, newValue) => {
                field.onChange(newValue?.id ?? ('' as VehicleId))
              }}
              renderInput={(params) => (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TextField
                    {...params}
                    label={ctIntl.formatMessage({ id: 'Vehicle' })}
                    helperText={ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    })}
                    error={!!fieldState.error}
                    size="small"
                  />
                  {isPending && <CircularProgress size={20} />}
                </Box>
              )}
            />
          )}
        />

        <Controller
          control={control}
          name="driverId"
          render={({ field, fieldState }) => (
            <DriverSingleSelect
              size="small"
              onChange={(newValue) => {
                field.onChange(newValue ? newValue.value : ('' as DriverId))
              }}
              driverId={field.value ?? null}
              promptName={ctIntl.formatMessage({ id: 'Driver' })}
              error={fieldState.error}
            />
          )}
        />

        <UserAutocomplete
          control={control}
          name="vehicleCommander"
          label={ctIntl.formatMessage({ id: 'Vehicle Commander' })}
          required
        />
      </Stack>
    </ConfirmationModal>
  )
}

export default ActivateBookingModal
