import {
  Box,
  FormControl,
  FormControlLabel,
  KarooFormStateContextProvider,
  Radio,
  RadioGroup,
  Stack,
  styled,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import { Controller, type UseFormReturn } from 'react-hook-form'
import { z } from 'zod'

import { driverGroupIdSchema } from 'api/types'
import { getSettings } from 'duxs/user'
import DriverGroupsMultiSelect from 'src/modules/shared/DriverGroupsMultiSelect'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/formik/messages'
import PhoneNumberInput, {
  getPhoneInfoSchema,
} from 'src/shared/react-hook-form/PhoneNumberInput'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { generateEmailSchema } from 'src/util-functions/zod-utils'

export const driverGeneralInfoSchema = () =>
  z.object({
    firstName: z.string().trim().min(1, messages.required),
    lastName: z.string().trim().nullable(),
    email: generateEmailSchema({ isRequired: true }),
    phoneInfo: getPhoneInfoSchema({ isRequired: false }),
    passport: z.string().trim().nullable(),
    gender: z.union([z.literal('male'), z.literal('female')]).nullable(),
    driverGroupIds: z.array(driverGroupIdSchema).min(1, messages.required),
  })

export type DriverGeneralInfoSchema = z.infer<
  ReturnType<typeof driverGeneralInfoSchema>
>

type Props = {
  form: UseFormReturn<DriverGeneralInfoSchema>
  isEditing: boolean
}

/**
 * Component used to create a driver or edit driver general info
 */
const DriverGeneralInfoForm = ({ form: { control }, isEditing }: Props) => {
  const { listDriverPassport } = useTypedSelector(getSettings)

  return (
    <KarooFormStateContextProvider value={{ readOnly: !isEditing }}>
      <Grid>
        <TextFieldControlled
          ControllerProps={{ control, name: 'firstName' }}
          required
          label={ctIntl.formatMessage({ id: 'First Name' })}
        />
        <TextFieldControlled
          ControllerProps={{ control, name: 'lastName' }}
          label={ctIntl.formatMessage({ id: 'Last Name' })}
        />
        <PhoneNumberInput
          ControllerProps={{ control, name: 'phoneInfo' }}
          label={ctIntl.formatMessage({ id: 'editUser.cellPhone.label' })}
        />
        <TextFieldControlled
          ControllerProps={{ control, name: 'email' }}
          required
          label={ctIntl.formatMessage({ id: 'Email' })}
        />

        {!!listDriverPassport && (
          <TextFieldControlled
            ControllerProps={{ control, name: 'passport' }}
            label={ctIntl.formatMessage({ id: 'ID/Passport' })}
          />
        )}

        <Controller
          control={control}
          name="driverGroupIds"
          render={({ field }) => (
            <DriverGroupsMultiSelect
              promptName={ctIntl.formatMessage({ id: 'Driver Group' })}
              groupIds={field.value}
              disabled={!isEditing}
              onChange={(driverGroups) =>
                field.onChange(driverGroups.map((g) => g.value))
              }
              textFieldProps={{
                required: true,
              }}
            />
          )}
        />

        <Controller
          control={control}
          name="gender"
          render={({ field }) => (
            <FormControl sx={{ flexDirection: 'row', alignItems: 'center', gap: 1 }}>
              <Stack
                direction="row"
                gap={2}
                alignItems="center"
              >
                <IntlTypography msgProps={{ id: 'Gender' }} />

                <RadioGroup
                  sx={{ flexDirection: 'row' }}
                  value={field.value}
                  onChange={field.onChange}
                >
                  <FormControlLabel
                    value="male"
                    control={<Radio size="small" />}
                    label={<IntlTypography msgProps={{ id: 'Male' }} />}
                  />
                  <FormControlLabel
                    value="female"
                    control={<Radio size="small" />}
                    label={<IntlTypography msgProps={{ id: 'Female' }} />}
                  />
                </RadioGroup>
              </Stack>
            </FormControl>
          )}
        />
      </Grid>
    </KarooFormStateContextProvider>
  )
}

const Grid = styled(Box)(({ theme }) =>
  theme.unstable_sx({
    gap: 2,
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
  }),
)

export default DriverGeneralInfoForm
